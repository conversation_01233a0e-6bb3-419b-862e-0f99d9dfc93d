#!/usr/bin/env python3
"""
测试交易UI应用程序是否能正常启动
"""

import subprocess
import time
import signal
import sys

def test_app():
    print("启动交易UI应用程序...")
    
    try:
        # 启动应用程序
        process = subprocess.Popen(
            ["./target/debug/trading-ui"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待几秒钟让应用程序启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 应用程序成功启动并正在运行")
            
            # 发送SIGTERM信号优雅地关闭应用程序
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=5)
                print("✅ 应用程序成功关闭")
            except subprocess.TimeoutExpired:
                print("⚠️  应用程序没有在5秒内关闭，强制终止")
                process.kill()
                process.wait()
                
        else:
            # 进程已经退出，检查退出码
            stdout, stderr = process.communicate()
            print(f"❌ 应用程序退出，退出码: {process.returncode}")
            if stdout:
                print(f"标准输出:\n{stdout}")
            if stderr:
                print(f"标准错误:\n{stderr}")
                
    except FileNotFoundError:
        print("❌ 找不到可执行文件 ./target/debug/trading-ui")
        print("请先运行 'cargo build' 编译项目")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    return True

if __name__ == "__main__":
    success = test_app()
    sys.exit(0 if success else 1)
