#!/usr/bin/env python3
"""
Simple WebSocket client to test the trading UI commands
"""

import asyncio
import websockets
import json
import sys

async def send_command(websocket, command_type, **kwargs):
    """Send a command to the trading UI"""
    message = {
        "type": command_type,
        **kwargs
    }
    
    print(f"Sending: {json.dumps(message, indent=2)}")
    await websocket.send(json.dumps(message))
    
    # Wait for response
    response = await websocket.recv()
    print(f"Response: {response}")
    print("-" * 50)

async def test_commands():
    """Test various trading commands"""
    uri = "ws://localhost:8080"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("Connected to trading UI WebSocket server")
            print("=" * 50)
            
            # Test buy order
            await send_command(websocket, "PlaceOrder",
                exchange="binance",
                symbol="BTCUSDT",
                side="BUY",
                quantity=0.1,
                price=45000.0,
                order_type="LIMIT"
            )
            
            # Test market sell order
            await send_command(websocket, "PlaceOrder",
                exchange="okx",
                symbol="ETHUSDT",
                side="SELL",
                quantity=1.0,
                order_type="MARKET"
            )
            
            # Test close position
            await send_command(websocket, "ClosePosition",
                exchange="binance",
                symbol="BTCUSDT",
                quantity=0.05
            )
            
            # Test withdrawal
            await send_command(websocket, "Withdraw",
                exchange="binance",
                asset="BTC",
                amount=0.1,
                address="**********************************"
            )
            
    except ConnectionRefusedError:
        print("Could not connect to WebSocket server. Make sure the trading UI is running.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("Trading UI WebSocket Client Test")
    print("Make sure to start the trading UI first with: cargo run")
    print()
    
    asyncio.run(test_commands())
