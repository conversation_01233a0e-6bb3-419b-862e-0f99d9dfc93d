use crate::state::SharedState;
use crate::types::{K<PERSON>, OrderBook, Position, Trade};
use chrono::Utc;
use std::collections::VecDeque;
use tokio::time::{Duration, sleep};
use tracing::{debug, info};
use uuid::Uuid;

/// 启动模拟数据生成器
pub async fn start_mock_data_generator(state: SharedState) {
    info!("Starting mock data generator");

    let symbols = vec!["btcusdt", "ethusdt", "adausdt"];
    let exchanges = vec!["binance", "okx", "bybit"];

    // 初始化一些持仓
    initialize_positions(&state, &exchanges, &symbols).await;

    // 注意：K线数据和24小时交易量统计将通过真实Binance数据更新
    info!("Mock data generator initialized - using real market data for quotes");

    let mut update_counter = 0u64;
    let volume_update_interval = 60000 / state.update_interval_ms; // 每分钟更新一次24小时交易量

    loop {
        // 注意：行情数据（订单簿、交易记录、K线）现在使用真实Binance数据
        // 这里只生成模拟的持仓和PnL数据

        // 更新PnL
        update_pnl(&state).await;

        // 更新持仓PnL
        update_positions_pnl(&state).await;

        // 每分钟更新一次24小时交易量统计
        if update_counter % volume_update_interval == 0 {
            for symbol in &symbols {
                state.update_24h_volume_stats(symbol);
            }
            debug!("Updated 24h volume statistics for all symbols");
        }

        // 清理旧数据
        state.cleanup_old_trades();

        update_counter += 1;
        sleep(Duration::from_millis(state.update_interval_ms)).await;
    }
}

/// 初始化持仓
async fn initialize_positions(state: &SharedState, exchanges: &[&str], symbols: &[&str]) {
    info!("Initializing mock positions");

    for exchange in exchanges {
        for symbol in symbols {
            let position = Position {
                exchange: exchange.to_string(),
                symbol: symbol.to_uppercase(),
                size: (random_f64() - 0.5) * 10.0, // Random position between -5 and 5
                entry_price: get_base_price(symbol) * (0.9 + random_f64() * 0.2), // ±10% from base
                mark_price: get_base_price(symbol) * (0.95 + random_f64() * 0.1), // ±5% from base
                unrealized_pnl: (random_f64() - 0.5) * 1000.0, // Random PnL
                side: if random_bool() {
                    "LONG".to_string()
                } else {
                    "SHORT".to_string()
                },
            };

            state.update_position(&format!("{}_{}", exchange, symbol), position);
        }
    }

    debug!("Initialized {} positions", exchanges.len() * symbols.len());
}

/// 更新订单簿
async fn update_orderbooks(state: &SharedState, symbols: &[&str]) {
    for symbol in symbols {
        let base_price = get_base_price(symbol);
        let spread = base_price * 0.001; // 0.1% spread
        let bid_price = base_price - spread / 2.0;
        let ask_price = base_price + spread / 2.0;

        // 生成随机订单簿
        let mut bids = Vec::new();
        let mut asks = Vec::new();

        for i in 0..5 {
            let bid_level = bid_price - (i as f64 * spread * 0.1);
            let ask_level = ask_price + (i as f64 * spread * 0.1);
            let qty = random_f64() * 10.0 + 1.0;

            bids.push((bid_level, qty));
            asks.push((ask_level, qty));
        }

        let orderbook = OrderBook {
            symbol: symbol.to_string(),
            bids,
            asks,
            last_update_id: random_u64(),
        };

        // 更新现货和期货订单簿
        state.update_orderbook(symbol, orderbook.clone(), false); // 现货
        state.update_orderbook(symbol, orderbook, true); // 期货
    }
}

/// 生成随机交易
async fn generate_trades(state: &SharedState, symbols: &[&str]) {
    for symbol in symbols {
        let base_price = get_base_price(symbol);

        let trade = Trade {
            id: Uuid::new_v4().to_string(),
            symbol: symbol.to_uppercase(),
            price: base_price + (random_f64() - 0.5) * base_price * 0.01,
            qty: random_f64() * 5.0 + 0.1,
            side: if random_bool() {
                "BUY".to_string()
            } else {
                "SELL".to_string()
            },
            timestamp: Utc::now(),
            is_maker: random_bool(),
        };

        // 添加到现货交易记录
        state.add_trade(symbol, trade.clone(), false, false);

        // 偶尔添加到期货交易记录
        if random_f64() < 0.3 {
            state.add_trade(symbol, trade, true, false);
        }
    }
}

/// 更新PnL
async fn update_pnl(_state: &SharedState) {
    // 更新现货和期货的未实现盈亏
    let new_spot_pnl = (random_f64() - 0.5) * 2000.0;
    let new_fut_pnl = (random_f64() - 0.5) * 5000.0;

    // 注意：这里需要使用内部可变性来更新值
    // 在实际实现中，可能需要使用 Mutex 或其他同步原语
    debug!(
        "Updated PnL - Spot: {:.2}, Futures: {:.2}",
        new_spot_pnl, new_fut_pnl
    );
}

/// 更新持仓PnL
async fn update_positions_pnl(state: &SharedState) {
    for mut entry in state.positions.iter_mut() {
        let position = entry.value_mut();

        // 更新标记价格（小幅波动）
        let price_change = (random_f64() - 0.5) * position.mark_price * 0.001;
        position.mark_price += price_change;

        // 重新计算未实现盈亏
        position.unrealized_pnl = (position.mark_price - position.entry_price) * position.size;
    }
}

/// 获取基础价格
fn get_base_price(symbol: &str) -> f64 {
    match symbol {
        "btcusdt" => 45000.0 + (random_f64() - 0.5) * 2000.0,
        "ethusdt" => 3000.0 + (random_f64() - 0.5) * 200.0,
        "adausdt" => 0.5 + (random_f64() - 0.5) * 0.1,
        _ => 100.0,
    }
}

/// 简单的随机数生成器
fn random_f64() -> f64 {
    use std::sync::atomic::{AtomicU64, Ordering};

    static SEED: AtomicU64 = AtomicU64::new(1);

    let seed = SEED.load(Ordering::Relaxed);
    let next = seed.wrapping_mul(1103515245).wrapping_add(12345);
    SEED.store(next, Ordering::Relaxed);

    (next as f64) / (u64::MAX as f64)
}

fn random_bool() -> bool {
    random_f64() > 0.5
}

fn random_u64() -> u64 {
    use std::sync::atomic::{AtomicU64, Ordering};

    static SEED: AtomicU64 = AtomicU64::new(1);

    let seed = SEED.load(Ordering::Relaxed);
    let next = seed.wrapping_mul(1103515245).wrapping_add(12345);
    SEED.store(next, Ordering::Relaxed);

    next
}
