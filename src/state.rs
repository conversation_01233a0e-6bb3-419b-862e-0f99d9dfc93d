use crate::types::{
    DepthDiff, DepthSnapshot, DepthState, InputMode, Kline, OrderBook, Position, Trade,
};
use dashmap::DashMap;
use std::collections::VecDeque;
use std::sync::{Arc, Mutex};
use tracing::debug;

/// 应用程序状态
#[derive(Debug)]
pub struct AppState {
    // Binance Spot
    pub spot_books: DashMap<String, OrderBook>,
    pub spot_trades: DashMap<String, VecDeque<Trade>>, // recent market trades
    pub spot_my_trades: DashMap<String, VecDeque<Trade>>, // my trades
    pub spot_unreal_pnl: f64,

    // Binance Futures
    pub fut_books: DashMap<String, OrderBook>,
    pub fut_trades: DashMap<String, VecDeque<Trade>>, // recent market trades
    pub fut_my_trades: DashMap<String, VecDeque<Trade>>, // my trades
    pub fut_unreal_pnl: f64,

    // All exchanges positions
    pub positions: DashMap<String, Position>, // key: exchange_symbol

    // K线数据 (1分钟K线)
    pub spot_klines: DashMap<String, VecDeque<Kline>>, // 现货K线数据
    pub futures_klines: DashMap<String, VecDeque<Kline>>, // 永续合约K线数据
    pub selected_symbol: Mutex<String>,                // 当前选择的交易对

    // 深度数据状态
    pub spot_depth_states: DashMap<String, DepthState>, // 现货深度状态
    pub futures_depth_states: DashMap<String, DepthState>, // 期货深度状态

    // UI state (需要内部可变性)
    pub input_mode: Mutex<InputMode>,
    pub command_input: Mutex<String>,
    pub status_message: Mutex<String>,
    pub selected_tab: Mutex<usize>,

    // Application settings
    pub max_trades_history: usize,
    pub update_interval_ms: u64,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            spot_books: DashMap::new(),
            spot_trades: DashMap::new(),
            spot_my_trades: DashMap::new(),
            spot_unreal_pnl: 0.0,
            fut_books: DashMap::new(),
            fut_trades: DashMap::new(),
            fut_my_trades: DashMap::new(),
            fut_unreal_pnl: 0.0,
            positions: DashMap::new(),
            spot_klines: DashMap::new(),
            futures_klines: DashMap::new(),
            selected_symbol: Mutex::new("BTCUSDT".to_string()),
            spot_depth_states: DashMap::new(),
            futures_depth_states: DashMap::new(),
            input_mode: Mutex::new(InputMode::Normal),
            command_input: Mutex::new(String::new()),
            status_message: Mutex::new("Ready".to_string()),
            selected_tab: Mutex::new(0),
            max_trades_history: 50,
            update_interval_ms: 500,
        }
    }
}

impl AppState {
    /// 创建新的应用状态
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置状态消息
    pub fn set_status(&self, message: String) {
        if let Ok(mut status) = self.status_message.lock() {
            *status = message;
        }
    }

    /// 获取状态消息
    pub fn get_status(&self) -> String {
        self.status_message
            .lock()
            .map(|guard| guard.clone())
            .unwrap_or_else(|_| "Error reading status".to_string())
    }

    /// 获取输入模式
    pub fn get_input_mode(&self) -> InputMode {
        self.input_mode
            .lock()
            .map(|guard| guard.clone())
            .unwrap_or_else(|_| InputMode::Normal)
    }

    /// 设置输入模式
    pub fn set_input_mode(&self, mode: InputMode) {
        if let Ok(mut input_mode) = self.input_mode.lock() {
            *input_mode = mode;
        }
    }

    /// 获取命令输入
    pub fn get_command_input(&self) -> String {
        self.command_input
            .lock()
            .map(|guard| guard.clone())
            .unwrap_or_else(|_| String::new())
    }

    /// 设置命令输入
    pub fn set_command_input(&self, input: String) {
        if let Ok(mut command_input) = self.command_input.lock() {
            *command_input = input;
        }
    }

    /// 清空命令输入
    pub fn clear_command_input(&self) {
        if let Ok(mut command_input) = self.command_input.lock() {
            command_input.clear();
        }
    }

    /// 向命令输入添加字符
    pub fn push_command_char(&self, c: char) {
        if let Ok(mut command_input) = self.command_input.lock() {
            command_input.push(c);
        }
    }

    /// 从命令输入删除最后一个字符
    pub fn pop_command_char(&self) {
        if let Ok(mut command_input) = self.command_input.lock() {
            command_input.pop();
        }
    }

    /// 获取选中的标签页
    pub fn get_selected_tab(&self) -> usize {
        self.selected_tab
            .lock()
            .map(|guard| *guard)
            .unwrap_or_else(|_| 0)
    }

    /// 设置选中的标签页
    pub fn set_selected_tab(&self, tab: usize) {
        if let Ok(mut selected_tab) = self.selected_tab.lock() {
            *selected_tab = tab.min(3); // 最多4个标签页 (添加了K线图标签页)
        }
    }

    /// 清理旧的交易记录
    pub fn cleanup_old_trades(&self) {
        let max_history = self.max_trades_history;

        // Clean spot trades
        for mut entry in self.spot_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }

        // Clean spot my trades
        for mut entry in self.spot_my_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }

        // Clean futures trades
        for mut entry in self.fut_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }

        // Clean futures my trades
        for mut entry in self.fut_my_trades.iter_mut() {
            entry.value_mut().truncate(max_history);
        }
    }

    /// 获取总的未实现盈亏
    pub fn get_total_unrealized_pnl(&self) -> f64 {
        let spot_pnl = self.spot_unreal_pnl;
        let fut_pnl = self.fut_unreal_pnl;
        let positions_pnl: f64 = self
            .positions
            .iter()
            .map(|entry| entry.value().unrealized_pnl)
            .sum();

        spot_pnl + fut_pnl + positions_pnl
    }

    /// 获取指定交易所的持仓
    pub fn get_positions_by_exchange(&self, exchange: &str) -> Vec<Position> {
        self.positions
            .iter()
            .filter(|entry| entry.value().exchange == exchange)
            .map(|entry| entry.value().clone())
            .collect()
    }

    /// 获取指定交易对的持仓
    pub fn get_positions_by_symbol(&self, symbol: &str) -> Vec<Position> {
        self.positions
            .iter()
            .filter(|entry| entry.value().symbol == symbol)
            .map(|entry| entry.value().clone())
            .collect()
    }

    /// 更新订单簿
    pub fn update_orderbook(&self, symbol: &str, orderbook: OrderBook, is_futures: bool) {
        if is_futures {
            self.fut_books.insert(symbol.to_string(), orderbook);
        } else {
            self.spot_books.insert(symbol.to_string(), orderbook);
        }
    }

    /// 添加交易记录
    pub fn add_trade(&self, symbol: &str, trade: Trade, is_futures: bool, is_my_trade: bool) {
        let trades_map = match (is_futures, is_my_trade) {
            (false, false) => &self.spot_trades,
            (false, true) => &self.spot_my_trades,
            (true, false) => &self.fut_trades,
            (true, true) => &self.fut_my_trades,
        };

        trades_map
            .entry(symbol.to_string())
            .or_insert_with(VecDeque::new)
            .push_front(trade);
    }

    /// 更新持仓
    pub fn update_position(&self, key: &str, position: Position) {
        self.positions.insert(key.to_string(), position);
    }

    /// 删除持仓
    pub fn remove_position(&self, key: &str) {
        self.positions.remove(key);
    }

    /// 获取选择的交易对
    pub fn get_selected_symbol(&self) -> String {
        self.selected_symbol
            .lock()
            .map(|guard| guard.clone())
            .unwrap_or_else(|_| "BTCUSDT".to_string())
    }

    /// 设置选择的交易对
    pub fn set_selected_symbol(&self, symbol: String) {
        if let Ok(mut selected_symbol) = self.selected_symbol.lock() {
            *selected_symbol = symbol;
        }
    }

    /// 添加K线数据
    pub fn add_kline(&self, symbol: &str, kline: Kline, is_futures: bool) {
        let klines_map = if is_futures {
            &self.futures_klines
        } else {
            &self.spot_klines
        };

        let mut klines = klines_map
            .entry(symbol.to_string())
            .or_insert_with(VecDeque::new);

        // 如果是新的K线或者更新现有K线
        if let Some(last_kline) = klines.back_mut() {
            if last_kline.open_time == kline.open_time {
                // 更新现有K线
                *last_kline = kline;
            } else {
                // 添加新K线
                klines.push_back(kline);
            }
        } else {
            // 第一根K线
            klines.push_back(kline);
        }

        // 保持最多200根K线
        if klines.len() > 200 {
            klines.pop_front();
        }
    }

    /// 获取K线数据
    pub fn get_klines(&self, symbol: &str, is_futures: bool) -> Vec<Kline> {
        let klines_map = if is_futures {
            &self.futures_klines
        } else {
            &self.spot_klines
        };

        klines_map
            .get(symbol)
            .map(|klines| klines.iter().cloned().collect())
            .unwrap_or_default()
    }

    /// 初始化深度快照
    pub fn initialize_depth_snapshot(
        &self,
        symbol: &str,
        snapshot: DepthSnapshot,
        is_futures: bool,
    ) {
        let depth_states = if is_futures {
            &self.futures_depth_states
        } else {
            &self.spot_depth_states
        };

        let depth_state = DepthState {
            snapshot: Some(snapshot.clone()),
            last_update_id: snapshot.last_update_id,
            is_initialized: true,
        };

        depth_states.insert(symbol.to_string(), depth_state);

        // 同时更新OrderBook以保持兼容性
        let orderbook = self.convert_snapshot_to_orderbook(&snapshot);
        self.update_orderbook(symbol, orderbook, is_futures);
    }

    /// 应用深度差分更新
    pub fn apply_depth_diff(&self, symbol: &str, diff: &DepthDiff, is_futures: bool) -> bool {
        let depth_states = if is_futures {
            &self.futures_depth_states
        } else {
            &self.spot_depth_states
        };

        if let Some(mut depth_state_ref) = depth_states.get_mut(symbol) {
            let depth_state = depth_state_ref.value_mut();

            // 检查是否已初始化
            if !depth_state.is_initialized {
                return false;
            }

            // 检查更新ID的连续性
            debug!(
                "Depth diff for {}: first_update_id={}, final_update_id={}, last_update_id={}",
                symbol, diff.first_update_id, diff.final_update_id, depth_state.last_update_id
            );

            // 根据Binance API文档的正确逻辑：
            // 1. 丢弃所有 final_update_id <= last_update_id 的差分数据
            // 2. 应用 first_update_id <= last_update_id + 1 的差分数据
            if diff.final_update_id <= depth_state.last_update_id {
                debug!(
                    "Dropping old depth diff for {}: final_update_id={} <= last_update_id={}",
                    symbol, diff.final_update_id, depth_state.last_update_id
                );
                return true; // 返回true表示处理成功，但是丢弃了旧数据
            }

            if diff.first_update_id <= depth_state.last_update_id + 1 {
                // 应用更新到快照
                if let Some(ref mut snapshot) = depth_state.snapshot {
                    debug!(
                        "Applying depth diff for {}: {} bids, {} asks",
                        symbol,
                        diff.bids.len(),
                        diff.asks.len()
                    );

                    self.apply_diff_to_snapshot(snapshot, diff);
                    depth_state.last_update_id = diff.final_update_id;

                    // 更新OrderBook以保持兼容性
                    let orderbook = self.convert_snapshot_to_orderbook(snapshot);
                    self.update_orderbook(symbol, orderbook, is_futures);

                    debug!(
                        "Successfully updated depth for {}, new last_update_id={}",
                        symbol, depth_state.last_update_id
                    );
                    return true;
                }
            } else {
                debug!(
                    "Skipping depth diff for {}: first_update_id={} > last_update_id + 1 = {}",
                    symbol,
                    diff.first_update_id,
                    depth_state.last_update_id + 1
                );
            }
        }
        false
    }

    /// 将快照转换为OrderBook格式
    fn convert_snapshot_to_orderbook(&self, snapshot: &DepthSnapshot) -> OrderBook {
        let bids: Vec<(f64, f64)> = snapshot
            .bids
            .iter()
            .filter_map(|(price, qty)| {
                let p = price.parse::<f64>().ok()?;
                let q = qty.parse::<f64>().ok()?;
                if q > 0.0 { Some((p, q)) } else { None }
            })
            .collect();

        let asks: Vec<(f64, f64)> = snapshot
            .asks
            .iter()
            .filter_map(|(price, qty)| {
                let p = price.parse::<f64>().ok()?;
                let q = qty.parse::<f64>().ok()?;
                if q > 0.0 { Some((p, q)) } else { None }
            })
            .collect();

        OrderBook {
            symbol: snapshot.symbol.clone(),
            bids,
            asks,
            last_update_id: snapshot.last_update_id,
        }
    }

    /// 将差分应用到快照
    fn apply_diff_to_snapshot(&self, snapshot: &mut DepthSnapshot, diff: &DepthDiff) {
        // 更新买盘
        for (price, qty) in &diff.bids {
            let qty_f64 = qty.parse::<f64>().unwrap_or(0.0);
            if qty_f64 == 0.0 {
                // 删除价格档位
                snapshot.bids.retain(|(p, _)| p != price);
            } else {
                // 更新或添加价格档位
                if let Some(pos) = snapshot.bids.iter().position(|(p, _)| p == price) {
                    snapshot.bids[pos] = (price.clone(), qty.clone());
                } else {
                    snapshot.bids.push((price.clone(), qty.clone()));
                }
            }
        }

        // 更新卖盘
        for (price, qty) in &diff.asks {
            let qty_f64 = qty.parse::<f64>().unwrap_or(0.0);
            if qty_f64 == 0.0 {
                // 删除价格档位
                snapshot.asks.retain(|(p, _)| p != price);
            } else {
                // 更新或添加价格档位
                if let Some(pos) = snapshot.asks.iter().position(|(p, _)| p == price) {
                    snapshot.asks[pos] = (price.clone(), qty.clone());
                } else {
                    snapshot.asks.push((price.clone(), qty.clone()));
                }
            }
        }

        // 按价格排序
        snapshot.bids.sort_by(|a, b| {
            let price_a = a.0.parse::<f64>().unwrap_or(0.0);
            let price_b = b.0.parse::<f64>().unwrap_or(0.0);
            price_b
                .partial_cmp(&price_a)
                .unwrap_or(std::cmp::Ordering::Equal) // 买盘降序
        });

        snapshot.asks.sort_by(|a, b| {
            let price_a = a.0.parse::<f64>().unwrap_or(0.0);
            let price_b = b.0.parse::<f64>().unwrap_or(0.0);
            price_a
                .partial_cmp(&price_b)
                .unwrap_or(std::cmp::Ordering::Equal) // 卖盘升序
        });

        // 更新最后更新ID
        snapshot.last_update_id = diff.final_update_id;
    }

    /// 获取深度状态
    pub fn get_depth_state(&self, symbol: &str, is_futures: bool) -> Option<DepthState> {
        let depth_states = if is_futures {
            &self.futures_depth_states
        } else {
            &self.spot_depth_states
        };

        depth_states.get(symbol).map(|state| state.clone())
    }
}

pub type SharedState = Arc<AppState>;
