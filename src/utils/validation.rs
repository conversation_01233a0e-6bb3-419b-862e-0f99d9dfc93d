use crate::types::{Exchange, OrderSide, OrderType};
use std::collections::HashSet;

/// 验证结果
pub type ValidationResult<T> = Result<T, ValidationError>;

/// 验证错误
#[derive(Debug, Clone, PartialEq)]
pub enum ValidationError {
    InvalidSymbol(String),
    InvalidExchange(String),
    InvalidQuantity(f64),
    InvalidPrice(f64),
    InvalidAddress(String),
    InvalidOrderType(String),
    InvalidSide(String),
    EmptyField(String),
    OutOfRange { field: String, min: f64, max: f64, value: f64 },
    Custom(String),
}

impl std::fmt::Display for ValidationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ValidationError::InvalidSymbol(symbol) => {
                write!(f, "Invalid trading symbol: {}", symbol)
            }
            ValidationError::InvalidExchange(exchange) => {
                write!(f, "Invalid exchange: {}", exchange)
            }
            ValidationError::InvalidQuantity(qty) => {
                write!(f, "Invalid quantity: {}", qty)
            }
            ValidationError::InvalidPrice(price) => {
                write!(f, "Invalid price: {}", price)
            }
            ValidationError::InvalidAddress(addr) => {
                write!(f, "Invalid address: {}", addr)
            }
            ValidationError::InvalidOrderType(order_type) => {
                write!(f, "Invalid order type: {}", order_type)
            }
            ValidationError::InvalidSide(side) => {
                write!(f, "Invalid order side: {}", side)
            }
            ValidationError::EmptyField(field) => {
                write!(f, "Field cannot be empty: {}", field)
            }
            ValidationError::OutOfRange { field, min, max, value } => {
                write!(f, "{} value {} is out of range [{}, {}]", field, value, min, max)
            }
            ValidationError::Custom(msg) => write!(f, "{}", msg),
        }
    }
}

impl std::error::Error for ValidationError {}

/// 交易验证器
pub struct TradeValidator {
    supported_symbols: HashSet<String>,
    supported_exchanges: HashSet<String>,
    min_quantity: f64,
    max_quantity: f64,
    min_price: f64,
    max_price: f64,
}

impl Default for TradeValidator {
    fn default() -> Self {
        let mut supported_symbols = HashSet::new();
        supported_symbols.insert("BTCUSDT".to_string());
        supported_symbols.insert("ETHUSDT".to_string());
        supported_symbols.insert("ADAUSDT".to_string());
        supported_symbols.insert("BNBUSDT".to_string());
        supported_symbols.insert("SOLUSDT".to_string());

        let mut supported_exchanges = HashSet::new();
        supported_exchanges.insert("binance".to_string());
        supported_exchanges.insert("okx".to_string());
        supported_exchanges.insert("bybit".to_string());
        supported_exchanges.insert("huobi".to_string());

        Self {
            supported_symbols,
            supported_exchanges,
            min_quantity: 0.000001,
            max_quantity: 1000000.0,
            min_price: 0.000001,
            max_price: 10000000.0,
        }
    }
}

impl TradeValidator {
    /// 创建新的验证器
    pub fn new() -> Self {
        Self::default()
    }

    /// 添加支持的交易对
    pub fn add_symbol(&mut self, symbol: String) {
        self.supported_symbols.insert(symbol.to_uppercase());
    }

    /// 添加支持的交易所
    pub fn add_exchange(&mut self, exchange: String) {
        self.supported_exchanges.insert(exchange.to_lowercase());
    }

    /// 验证交易对
    pub fn validate_symbol(&self, symbol: &str) -> ValidationResult<String> {
        let symbol = symbol.to_uppercase();
        if self.supported_symbols.contains(&symbol) {
            Ok(symbol)
        } else {
            Err(ValidationError::InvalidSymbol(symbol))
        }
    }

    /// 验证交易所
    pub fn validate_exchange(&self, exchange: &str) -> ValidationResult<String> {
        let exchange = exchange.to_lowercase();
        if self.supported_exchanges.contains(&exchange) {
            Ok(exchange)
        } else {
            Err(ValidationError::InvalidExchange(exchange))
        }
    }

    /// 验证数量
    pub fn validate_quantity(&self, quantity: f64) -> ValidationResult<f64> {
        if quantity <= 0.0 {
            return Err(ValidationError::InvalidQuantity(quantity));
        }

        if quantity < self.min_quantity || quantity > self.max_quantity {
            return Err(ValidationError::OutOfRange {
                field: "quantity".to_string(),
                min: self.min_quantity,
                max: self.max_quantity,
                value: quantity,
            });
        }

        Ok(quantity)
    }

    /// 验证价格
    pub fn validate_price(&self, price: Option<f64>) -> ValidationResult<Option<f64>> {
        match price {
            Some(p) => {
                if p <= 0.0 {
                    return Err(ValidationError::InvalidPrice(p));
                }

                if p < self.min_price || p > self.max_price {
                    return Err(ValidationError::OutOfRange {
                        field: "price".to_string(),
                        min: self.min_price,
                        max: self.max_price,
                        value: p,
                    });
                }

                Ok(Some(p))
            }
            None => Ok(None), // Market orders don't have price
        }
    }

    /// 验证订单方向
    pub fn validate_side(&self, side: &str) -> ValidationResult<OrderSide> {
        match side.to_uppercase().as_str() {
            "BUY" => Ok(OrderSide::Buy),
            "SELL" => Ok(OrderSide::Sell),
            _ => Err(ValidationError::InvalidSide(side.to_string())),
        }
    }

    /// 验证订单类型
    pub fn validate_order_type(&self, order_type: &str) -> ValidationResult<OrderType> {
        match order_type.to_uppercase().as_str() {
            "MARKET" => Ok(OrderType::Market),
            "LIMIT" => Ok(OrderType::Limit),
            "STOP_LOSS" => Ok(OrderType::StopLoss),
            "TAKE_PROFIT" => Ok(OrderType::TakeProfit),
            _ => Err(ValidationError::InvalidOrderType(order_type.to_string())),
        }
    }

    /// 验证完整的下单请求
    pub fn validate_order(
        &self,
        exchange: &str,
        symbol: &str,
        side: &str,
        quantity: f64,
        price: Option<f64>,
        order_type: &str,
    ) -> ValidationResult<()> {
        self.validate_exchange(exchange)?;
        self.validate_symbol(symbol)?;
        self.validate_side(side)?;
        self.validate_quantity(quantity)?;
        self.validate_price(price)?;
        self.validate_order_type(order_type)?;

        // 验证价格和订单类型的一致性
        match (price, order_type.to_uppercase().as_str()) {
            (None, "LIMIT") => {
                return Err(ValidationError::Custom(
                    "Limit orders must have a price".to_string(),
                ));
            }
            (Some(_), "MARKET") => {
                return Err(ValidationError::Custom(
                    "Market orders should not have a price".to_string(),
                ));
            }
            _ => {}
        }

        Ok(())
    }
}

/// 地址验证器
pub struct AddressValidator;

impl AddressValidator {
    /// 验证比特币地址
    pub fn validate_bitcoin_address(address: &str) -> ValidationResult<String> {
        if address.is_empty() {
            return Err(ValidationError::EmptyField("address".to_string()));
        }

        // 简单的比特币地址验证
        if address.len() < 26 || address.len() > 62 {
            return Err(ValidationError::InvalidAddress(
                "Bitcoin address length invalid".to_string(),
            ));
        }

        // 检查地址前缀
        if !address.starts_with('1')
            && !address.starts_with('3')
            && !address.starts_with("bc1")
        {
            return Err(ValidationError::InvalidAddress(
                "Invalid Bitcoin address prefix".to_string(),
            ));
        }

        Ok(address.to_string())
    }

    /// 验证以太坊地址
    pub fn validate_ethereum_address(address: &str) -> ValidationResult<String> {
        if address.is_empty() {
            return Err(ValidationError::EmptyField("address".to_string()));
        }

        // 以太坊地址验证
        if !address.starts_with("0x") || address.len() != 42 {
            return Err(ValidationError::InvalidAddress(
                "Invalid Ethereum address format".to_string(),
            ));
        }

        // 检查是否为有效的十六进制
        if !address[2..].chars().all(|c| c.is_ascii_hexdigit()) {
            return Err(ValidationError::InvalidAddress(
                "Invalid Ethereum address characters".to_string(),
            ));
        }

        Ok(address.to_string())
    }

    /// 根据资产类型验证地址
    pub fn validate_address(asset: &str, address: &str) -> ValidationResult<String> {
        match asset.to_uppercase().as_str() {
            "BTC" => Self::validate_bitcoin_address(address),
            "ETH" | "USDT" | "USDC" => Self::validate_ethereum_address(address),
            _ => {
                // 对于其他资产，进行基本验证
                if address.is_empty() {
                    Err(ValidationError::EmptyField("address".to_string()))
                } else if address.len() < 10 {
                    Err(ValidationError::InvalidAddress(
                        "Address too short".to_string(),
                    ))
                } else {
                    Ok(address.to_string())
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_symbol_validation() {
        let validator = TradeValidator::new();
        
        assert!(validator.validate_symbol("BTCUSDT").is_ok());
        assert!(validator.validate_symbol("btcusdt").is_ok());
        assert!(validator.validate_symbol("INVALID").is_err());
    }

    #[test]
    fn test_quantity_validation() {
        let validator = TradeValidator::new();
        
        assert!(validator.validate_quantity(1.0).is_ok());
        assert!(validator.validate_quantity(0.0).is_err());
        assert!(validator.validate_quantity(-1.0).is_err());
    }

    #[test]
    fn test_bitcoin_address_validation() {
        assert!(AddressValidator::validate_bitcoin_address("**********************************").is_ok());
        assert!(AddressValidator::validate_bitcoin_address("**********************************").is_ok());
        assert!(AddressValidator::validate_bitcoin_address("******************************************").is_ok());
        assert!(AddressValidator::validate_bitcoin_address("invalid").is_err());
    }

    #[test]
    fn test_ethereum_address_validation() {
        assert!(AddressValidator::validate_ethereum_address("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8").is_err()); // Wrong length
        assert!(AddressValidator::validate_ethereum_address("******************************************").is_ok());
        assert!(AddressValidator::validate_ethereum_address("742d35Cc6634C0532925a3b8D4C9db96C4b4Df8C").is_err()); // No 0x prefix
    }
}
