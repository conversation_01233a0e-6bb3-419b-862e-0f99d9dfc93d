use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

/// 应用程序配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// WebSocket服务器配置
    pub websocket: WebSocketConfig,
    /// UI配置
    pub ui: UiConfig,
    /// 数据配置
    pub data: DataConfig,
    /// 日志配置
    pub logging: LoggingConfig,
}

/// WebSocket配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketConfig {
    /// 监听地址
    pub bind_address: String,
    /// 监听端口
    pub port: u16,
    /// 最大连接数
    pub max_connections: usize,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
}

/// UI配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UiConfig {
    /// 刷新率（FPS）
    pub refresh_rate: u16,
    /// 默认选中的标签页
    pub default_tab: usize,
    /// 最大交易历史记录数
    pub max_trade_history: usize,
    /// 颜色主题
    pub theme: String,
}

/// 数据配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataConfig {
    /// 数据更新间隔（毫秒）
    pub update_interval_ms: u64,
    /// 支持的交易对
    pub symbols: Vec<String>,
    /// 支持的交易所
    pub exchanges: Vec<String>,
    /// 是否启用模拟数据
    pub enable_mock_data: bool,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 是否记录到文件
    pub log_to_file: bool,
    /// 日志目录
    pub log_dir: String,
    /// 文件轮转策略
    pub rotation: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            websocket: WebSocketConfig::default(),
            ui: UiConfig::default(),
            data: DataConfig::default(),
            logging: LoggingConfig::default(),
        }
    }
}

impl Default for WebSocketConfig {
    fn default() -> Self {
        Self {
            bind_address: "127.0.0.1".to_string(),
            port: 8080,
            max_connections: 100,
            heartbeat_interval: 30,
        }
    }
}

impl Default for UiConfig {
    fn default() -> Self {
        Self {
            refresh_rate: 60,
            default_tab: 0,
            max_trade_history: 50,
            theme: "default".to_string(),
        }
    }
}

impl Default for DataConfig {
    fn default() -> Self {
        Self {
            update_interval_ms: 500,
            symbols: vec![
                "BTCUSDT".to_string(),
                "ETHUSDT".to_string(),
                "ADAUSDT".to_string(),
            ],
            exchanges: vec![
                "binance".to_string(),
                "okx".to_string(),
                "bybit".to_string(),
            ],
            enable_mock_data: true,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            log_to_file: true,
            log_dir: "logs".to_string(),
            rotation: "daily".to_string(),
        }
    }
}

impl AppConfig {
    /// 从文件加载配置
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let content = fs::read_to_string(path)?;
        let config: AppConfig = toml::from_str(&content)?;
        Ok(config)
    }
    
    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<(), Box<dyn std::error::Error>> {
        let content = toml::to_string_pretty(self)?;
        fs::write(path, content)?;
        Ok(())
    }
    
    /// 加载配置，如果文件不存在则使用默认配置
    pub fn load_or_default<P: AsRef<Path>>(path: P) -> Self {
        Self::load_from_file(&path).unwrap_or_else(|_| {
            let config = Self::default();
            // 尝试保存默认配置
            let _ = config.save_to_file(path);
            config
        })
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<(), String> {
        // 验证WebSocket配置
        if self.websocket.port == 0 {
            return Err("WebSocket port cannot be 0".to_string());
        }
        
        if self.websocket.max_connections == 0 {
            return Err("Max connections cannot be 0".to_string());
        }
        
        // 验证UI配置
        if self.ui.refresh_rate == 0 {
            return Err("Refresh rate cannot be 0".to_string());
        }
        
        if self.ui.default_tab > 2 {
            return Err("Default tab must be 0, 1, or 2".to_string());
        }
        
        // 验证数据配置
        if self.data.update_interval_ms == 0 {
            return Err("Update interval cannot be 0".to_string());
        }
        
        if self.data.symbols.is_empty() {
            return Err("At least one symbol must be configured".to_string());
        }
        
        if self.data.exchanges.is_empty() {
            return Err("At least one exchange must be configured".to_string());
        }
        
        Ok(())
    }
    
    /// 获取WebSocket完整地址
    pub fn websocket_address(&self) -> String {
        format!("{}:{}", self.websocket.bind_address, self.websocket.port)
    }
    
    /// 获取刷新间隔（毫秒）
    pub fn refresh_interval_ms(&self) -> u64 {
        1000 / self.ui.refresh_rate as u64
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    
    #[test]
    fn test_default_config() {
        let config = AppConfig::default();
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_config_serialization() {
        let config = AppConfig::default();
        let toml_str = toml::to_string(&config).unwrap();
        let deserialized: AppConfig = toml::from_str(&toml_str).unwrap();
        
        assert_eq!(config.websocket.port, deserialized.websocket.port);
        assert_eq!(config.ui.refresh_rate, deserialized.ui.refresh_rate);
    }
    
    #[test]
    fn test_config_file_operations() {
        let temp_file = NamedTempFile::new().unwrap();
        let config = AppConfig::default();
        
        // 保存配置
        config.save_to_file(temp_file.path()).unwrap();
        
        // 加载配置
        let loaded_config = AppConfig::load_from_file(temp_file.path()).unwrap();
        
        assert_eq!(config.websocket.port, loaded_config.websocket.port);
    }
}
