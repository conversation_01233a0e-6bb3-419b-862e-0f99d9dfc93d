use crossterm::{
    event::{self, Event},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ratatui::{Terminal, backend::CrosstermBackend};
use std::{
    error::Error,
    io,
    time::{Duration, Instant},
};
use tokio::{
    select,
    sync::{broadcast, mpsc},
    time::sleep,
};
use tracing::info;
use trading_ui::{
    // data::start_mock_data_generator,
    state::{AppState, SharedState},
    types::{UiEvent, WsMessage},
    ui::app::App,
    utils::logging::init_ui_logging,
    websocket::{BinanceWsClient, start_message_handler, start_mock_server},
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    // 初始化日志系统
    init_ui_logging()?;
    info!("Starting Trading UI application");

    // 创建共享状态
    let state: SharedState = std::sync::Arc::new(AppState::new());

    // 创建通信通道
    let (ui_tx, ui_rx) = broadcast::channel::<UiEvent>(16);
    let (ws_tx, ws_rx) = mpsc::unbounded_channel();

    // 启动后台任务
    start_background_tasks(state.clone(), ui_tx.clone(), ws_tx.clone(), ws_rx).await?;

    // 初始化终端
    let terminal = setup_terminal()?;

    // 创建应用实例
    let app = App::new(state);

    // 运行主循环
    let result = run_main_loop(terminal, app, ui_rx, ws_tx).await;

    // 清理终端
    cleanup_terminal()?;

    result
}

/// 启动后台任务
async fn start_background_tasks(
    state: SharedState,
    ui_tx: broadcast::Sender<UiEvent>,
    ws_tx: mpsc::UnboundedSender<WsMessage>,
    ws_rx: mpsc::UnboundedReceiver<WsMessage>,
) -> Result<(), Box<dyn Error>> {
    info!("Starting background tasks");

    // 1. WebSocket服务器
    let ws_state = state.clone();
    let ws_sender = ws_tx.clone();
    tokio::spawn(async move {
        if let Err(e) = start_mock_server(ws_state, ws_sender).await {
            tracing::error!("WebSocket server error: {}", e);
        }
    });

    // 2. 消息处理器
    let msg_state = state.clone();
    tokio::spawn(async move {
        start_message_handler(msg_state, ws_rx).await;
    });

    // 3. 模拟数据生成器 (暂时禁用，使用真实数据)
    // let data_state = state.clone();
    // tokio::spawn(async move {
    //     start_mock_data_generator(data_state).await;
    // });

    // 4. Binance WebSocket客户端
    let binance_state = state.clone();
    tokio::spawn(async move {
        let binance_client = BinanceWsClient::new(binance_state.clone());
        let selected_symbol = binance_state.get_selected_symbol();

        // 启动K线数据流
        if let Err(e) = binance_client.start_kline_streams(&selected_symbol).await {
            tracing::error!("Failed to start Binance kline streams: {}", e);
        }

        // 启动深度数据流
        if let Err(e) = binance_client.start_depth_streams(&selected_symbol).await {
            tracing::error!("Failed to start Binance depth streams: {}", e);
        }

        // 监听交易对变化并重新订阅
        let mut current_symbol = selected_symbol;
        loop {
            sleep(Duration::from_secs(1)).await;
            let new_symbol = binance_state.get_selected_symbol();
            if new_symbol != current_symbol {
                info!(
                    "Symbol changed from {} to {}, restarting streams",
                    current_symbol, new_symbol
                );
                current_symbol = new_symbol.clone();

                // 重新启动K线数据流
                if let Err(e) = binance_client.start_kline_streams(&new_symbol).await {
                    tracing::error!("Failed to restart Binance kline streams: {}", e);
                }

                // 重新启动深度数据流
                if let Err(e) = binance_client.start_depth_streams(&new_symbol).await {
                    tracing::error!("Failed to restart Binance depth streams: {}", e);
                }
            }
        }
    });

    // 5. UI事件生成器
    tokio::spawn(async move {
        loop {
            let _ = ui_tx.send(UiEvent::Tick);
            sleep(Duration::from_millis(16)).await; // 60 FPS
        }
    });

    Ok(())
}

/// 设置终端
fn setup_terminal() -> Result<Terminal<CrosstermBackend<io::Stdout>>, Box<dyn Error>> {
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen)?;
    let backend = CrosstermBackend::new(stdout);
    let terminal = Terminal::new(backend)?;
    info!("Terminal initialized");
    Ok(terminal)
}

/// 清理终端
fn cleanup_terminal() -> Result<(), Box<dyn Error>> {
    disable_raw_mode()?;
    execute!(io::stdout(), LeaveAlternateScreen)?;
    info!("Terminal cleaned up");
    Ok(())
}

/// 运行主循环
async fn run_main_loop(
    mut terminal: Terminal<CrosstermBackend<io::Stdout>>,
    mut app: App,
    mut ui_rx: broadcast::Receiver<UiEvent>,
    ws_tx: mpsc::UnboundedSender<WsMessage>,
) -> Result<(), Box<dyn Error>> {
    info!("Starting main UI loop");
    let mut last_tick = Instant::now();

    loop {
        // 渲染UI
        terminal.draw(|f| app.render(f))?;

        // 处理事件
        let timeout = Duration::from_millis(16).saturating_sub(last_tick.elapsed());
        select! {
            biased;
            Ok(ev) = ui_rx.recv() => {
                match ev {
                    UiEvent::Tick => last_tick = Instant::now(),
                    UiEvent::Resize(_, _) => {},
                    UiEvent::KeyPress(key) => {
                        if app.handle_key_event(key, &ws_tx).await {
                            break;
                        }
                    }
                }
            }
            Ok(true) = async { Ok::<bool, io::Error>(event::poll(timeout)?) } => {
                if let Event::Key(key) = event::read()? {
                    if app.handle_key_event(key, &ws_tx).await {
                        break;
                    }
                }
            }
        }

        // 检查是否应该退出
        if app.should_quit() {
            break;
        }
    }

    info!("Main UI loop ended");
    Ok(())
}
