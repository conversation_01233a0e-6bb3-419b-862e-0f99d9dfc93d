use crate::types::WsMessage;
use futures_util::{SinkExt, StreamExt};
use std::error::Error;
use tokio_tungstenite::{connect_async, tungstenite::Message};

/// WebSocket客户端
pub struct WebSocketClient {
    url: String,
}

impl WebSocketClient {
    /// 创建新的WebSocket客户端
    pub fn new(url: String) -> Self {
        Self { url }
    }
    
    /// 连接到WebSocket服务器并发送消息
    pub async fn send_message(&self, message: WsMessage) -> Result<WsMessage, Box<dyn Error>> {
        let (ws_stream, _) = connect_async(&self.url).await?;
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();
        
        // 发送消息
        let message_text = serde_json::to_string(&message)?;
        ws_sender.send(Message::Text(message_text)).await?;
        
        // 等待响应
        if let Some(msg) = ws_receiver.next().await {
            match msg? {
                Message::Text(text) => {
                    let response: WsMessage = serde_json::from_str(&text)?;
                    Ok(response)
                }
                _ => Err("Unexpected message type".into()),
            }
        } else {
            Err("No response received".into())
        }
    }
    
    /// 测试连接
    pub async fn test_connection(&self) -> Result<bool, Box<dyn Error>> {
        let (ws_stream, _) = connect_async(&self.url).await?;
        let (mut ws_sender, _) = ws_stream.split();
        
        // 发送ping
        ws_sender.send(Message::Ping(vec![])).await?;
        
        Ok(true)
    }
}

/// 创建测试客户端
pub fn create_test_client() -> WebSocketClient {
    WebSocketClient::new("ws://127.0.0.1:8080".to_string())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::WsMessage;
    
    #[tokio::test]
    async fn test_websocket_client() {
        let client = create_test_client();
        
        // 测试下单消息
        let order_msg = WsMessage::PlaceOrder {
            exchange: "binance".to_string(),
            symbol: "BTCUSDT".to_string(),
            side: "BUY".to_string(),
            quantity: 0.1,
            price: Some(50000.0),
            order_type: "LIMIT".to_string(),
        };
        
        // 注意：这个测试需要WebSocket服务器运行
        // 在实际测试中，你可能需要启动一个测试服务器
        match client.send_message(order_msg).await {
            Ok(response) => {
                println!("Received response: {:?}", response);
            }
            Err(e) => {
                println!("Test failed (server might not be running): {}", e);
            }
        }
    }
}
