use crate::types::{WsMessage, Exchange, OrderType, OrderSide};

/// 命令解析器
pub struct CommandParser;

impl CommandParser {
    /// 解析用户输入的命令
    pub fn parse(command: &str) -> Option<WsMessage> {
        let parts: Vec<&str> = command.trim().split_whitespace().collect();
        if parts.is_empty() {
            return None;
        }

        match parts[0].to_lowercase().as_str() {
            "buy" | "sell" => Self::parse_order_command(&parts),
            "close" => Self::parse_close_command(&parts),
            "withdraw" => Self::parse_withdraw_command(&parts),
            "help" => Self::parse_help_command(),
            _ => None,
        }
    }
    
    /// 解析下单命令
    /// 格式: buy/sell <exchange> <symbol> <quantity> [price]
    fn parse_order_command(parts: &[&str]) -> Option<WsMessage> {
        if parts.len() < 4 {
            return None;
        }
        
        let side = parts[0].to_uppercase();
        let exchange = parts[1].to_string();
        let symbol = parts[2].to_uppercase();
        let quantity: f64 = parts[3].parse().ok()?;
        
        let price = if parts.len() > 4 {
            parts[4].parse().ok()
        } else {
            None
        };
        
        // 验证交易所
        if Exchange::from_str(&exchange).is_none() {
            return None;
        }
        
        Some(WsMessage::PlaceOrder {
            exchange,
            symbol,
            side,
            quantity,
            price,
            order_type: if price.is_some() {
                OrderType::Limit.as_str().to_string()
            } else {
                OrderType::Market.as_str().to_string()
            },
        })
    }
    
    /// 解析平仓命令
    /// 格式: close <exchange> <symbol> [quantity]
    fn parse_close_command(parts: &[&str]) -> Option<WsMessage> {
        if parts.len() < 3 {
            return None;
        }
        
        let exchange = parts[1].to_string();
        let symbol = parts[2].to_uppercase();
        let quantity = if parts.len() > 3 {
            parts[3].parse().ok()
        } else {
            None
        };
        
        // 验证交易所
        if Exchange::from_str(&exchange).is_none() {
            return None;
        }
        
        Some(WsMessage::ClosePosition {
            exchange,
            symbol,
            quantity,
        })
    }
    
    /// 解析提币命令
    /// 格式: withdraw <exchange> <asset> <amount> <address>
    fn parse_withdraw_command(parts: &[&str]) -> Option<WsMessage> {
        if parts.len() < 5 {
            return None;
        }
        
        let exchange = parts[1].to_string();
        let asset = parts[2].to_uppercase();
        let amount: f64 = parts[3].parse().ok()?;
        let address = parts[4].to_string();
        
        // 验证交易所
        if Exchange::from_str(&exchange).is_none() {
            return None;
        }
        
        // 简单的地址格式验证
        if address.len() < 10 {
            return None;
        }
        
        Some(WsMessage::Withdraw {
            exchange,
            asset,
            amount,
            address,
        })
    }
    
    /// 解析帮助命令
    fn parse_help_command() -> Option<WsMessage> {
        // 帮助命令不需要发送到WebSocket，直接在UI中处理
        None
    }
    
    /// 获取命令帮助信息
    pub fn get_help_text() -> Vec<&'static str> {
        vec![
            "Available Commands:",
            "",
            "Trading:",
            "  buy <exchange> <symbol> <qty> [price]   - Place buy order",
            "  sell <exchange> <symbol> <qty> [price]  - Place sell order",
            "  close <exchange> <symbol> [qty]         - Close position",
            "",
            "Withdrawal:",
            "  withdraw <exchange> <asset> <amount> <address>",
            "",
            "Examples:",
            "  buy binance BTCUSDT 0.1 50000    - Limit buy order",
            "  sell okx ETHUSDT 1.0             - Market sell order",
            "  close binance BTCUSDT 0.5        - Close partial position",
            "  close binance BTCUSDT            - Close all position",
            "  withdraw binance BTC 0.5 **********************************",
            "",
            "Supported Exchanges:",
            "  binance, okx, bybit, huobi",
            "",
            "Navigation:",
            "  Tab - Switch tabs",
            "  :   - Enter command mode",
            "  Esc - Exit command mode",
            "  q   - Quit application",
        ]
    }
    
    /// 验证命令格式
    pub fn validate_command(command: &str) -> Result<(), String> {
        let parts: Vec<&str> = command.trim().split_whitespace().collect();
        if parts.is_empty() {
            return Err("Empty command".to_string());
        }
        
        match parts[0].to_lowercase().as_str() {
            "buy" | "sell" => {
                if parts.len() < 4 {
                    return Err("Usage: buy/sell <exchange> <symbol> <quantity> [price]".to_string());
                }
                
                // 验证数量
                if parts[3].parse::<f64>().is_err() {
                    return Err("Invalid quantity format".to_string());
                }
                
                // 验证价格（如果提供）
                if parts.len() > 4 && parts[4].parse::<f64>().is_err() {
                    return Err("Invalid price format".to_string());
                }
                
                // 验证交易所
                if Exchange::from_str(parts[1]).is_none() {
                    return Err(format!("Unsupported exchange: {}", parts[1]));
                }
            }
            "close" => {
                if parts.len() < 3 {
                    return Err("Usage: close <exchange> <symbol> [quantity]".to_string());
                }
                
                // 验证数量（如果提供）
                if parts.len() > 3 && parts[3].parse::<f64>().is_err() {
                    return Err("Invalid quantity format".to_string());
                }
                
                // 验证交易所
                if Exchange::from_str(parts[1]).is_none() {
                    return Err(format!("Unsupported exchange: {}", parts[1]));
                }
            }
            "withdraw" => {
                if parts.len() < 5 {
                    return Err("Usage: withdraw <exchange> <asset> <amount> <address>".to_string());
                }
                
                // 验证金额
                if parts[3].parse::<f64>().is_err() {
                    return Err("Invalid amount format".to_string());
                }
                
                // 验证交易所
                if Exchange::from_str(parts[1]).is_none() {
                    return Err(format!("Unsupported exchange: {}", parts[1]));
                }
                
                // 简单的地址验证
                if parts[4].len() < 10 {
                    return Err("Address too short".to_string());
                }
            }
            "help" => {
                // 帮助命令总是有效的
            }
            _ => {
                return Err(format!("Unknown command: {}", parts[0]));
            }
        }
        
        Ok(())
    }
}
