use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;

/// 订单簿数据结构
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct OrderBook {
    pub bids: Vec<(f64, f64)>, // price, quantity
    pub asks: Vec<(f64, f64)>,
    pub last_update_id: u64,
    pub symbol: String,
}

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    pub id: String,
    pub symbol: String,
    pub price: f64,
    pub qty: f64,
    pub side: String, // "BUY" or "SELL"
    pub timestamp: DateTime<Utc>,
    pub is_maker: bool,
}

/// 持仓信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub exchange: String,
    pub symbol: String,
    pub size: f64,
    pub entry_price: f64,
    pub mark_price: f64,
    pub unrealized_pnl: f64,
    pub side: String, // "LONG" or "SHORT"
}

/// WebSocket消息类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WsMessage {
    // Market data updates
    OrderBookUpdate {
        data: OrderBook,
    },
    TradeUpdate {
        data: Trade,
    },
    PositionUpdate {
        data: Position,
    },
    KlineUpdate {
        data: Kline,
    },

    // Commands
    PlaceOrder {
        exchange: String,
        symbol: String,
        side: String,
        quantity: f64,
        price: Option<f64>, // None for market orders
        order_type: String, // "MARKET", "LIMIT"
    },
    ClosePosition {
        exchange: String,
        symbol: String,
        quantity: Option<f64>, // None to close all
    },
    Withdraw {
        exchange: String,
        asset: String,
        amount: f64,
        address: String,
    },

    // Responses
    CommandResult {
        success: bool,
        message: String,
        order_id: Option<String>,
    },
}

/// UI事件类型
#[derive(Debug, Clone)]
pub enum UiEvent {
    Tick,
    Resize(u16, u16),
    KeyPress(crossterm::event::KeyEvent),
}

/// 输入模式
#[derive(Debug, Clone, PartialEq)]
pub enum InputMode {
    Normal,
    Command,
}

/// 交易所类型
#[derive(Debug, Clone, PartialEq)]
pub enum Exchange {
    Binance,
    Okx,
    Bybit,
    Huobi,
}

impl Exchange {
    pub fn as_str(&self) -> &'static str {
        match self {
            Exchange::Binance => "binance",
            Exchange::Okx => "okx",
            Exchange::Bybit => "bybit",
            Exchange::Huobi => "huobi",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "binance" => Some(Exchange::Binance),
            "okx" => Some(Exchange::Okx),
            "bybit" => Some(Exchange::Bybit),
            "huobi" => Some(Exchange::Huobi),
            _ => None,
        }
    }
}

/// 订单类型
#[derive(Debug, Clone, PartialEq)]
pub enum OrderType {
    Market,
    Limit,
    StopLoss,
    TakeProfit,
}

impl OrderType {
    pub fn as_str(&self) -> &'static str {
        match self {
            OrderType::Market => "MARKET",
            OrderType::Limit => "LIMIT",
            OrderType::StopLoss => "STOP_LOSS",
            OrderType::TakeProfit => "TAKE_PROFIT",
        }
    }
}

/// 订单方向
#[derive(Debug, Clone, PartialEq)]
pub enum OrderSide {
    Buy,
    Sell,
}

impl OrderSide {
    pub fn as_str(&self) -> &'static str {
        match self {
            OrderSide::Buy => "BUY",
            OrderSide::Sell => "SELL",
        }
    }
}

/// 持仓方向
#[derive(Debug, Clone, PartialEq)]
pub enum PositionSide {
    Long,
    Short,
}

impl PositionSide {
    pub fn as_str(&self) -> &'static str {
        match self {
            PositionSide::Long => "LONG",
            PositionSide::Short => "SHORT",
        }
    }
}

/// K线数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Kline {
    pub symbol: String,
    pub open_time: i64,              // 开盘时间戳 (毫秒)
    pub close_time: i64,             // 收盘时间戳 (毫秒)
    pub open_price: f64,             // 开盘价
    pub high_price: f64,             // 最高价
    pub low_price: f64,              // 最低价
    pub close_price: f64,            // 收盘价
    pub volume: f64,                 // 成交量
    pub quote_volume: f64,           // 成交额
    pub trade_count: i64,            // 成交笔数
    pub taker_buy_volume: f64,       // 主动买入成交量
    pub taker_buy_quote_volume: f64, // 主动买入成交额
    pub is_closed: bool,             // 这根K线是否完结
}

/// 深度快照数据结构 (Binance depth snapshot)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DepthSnapshot {
    pub symbol: String,
    pub last_update_id: u64,         // 最后更新ID
    pub bids: Vec<(String, String)>, // 买盘 [价格, 数量]
    pub asks: Vec<(String, String)>, // 卖盘 [价格, 数量]
}

/// 深度差分数据结构 (Binance depth diff stream)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DepthDiff {
    #[serde(rename = "e")]
    pub event_type: String, // 事件类型 "depthUpdate"
    #[serde(rename = "E")]
    pub event_time: u64, // 事件时间
    #[serde(rename = "s")]
    pub symbol: String, // 交易对
    #[serde(rename = "U")]
    pub first_update_id: u64, // 从上次推送至今新增的第一个 update Id
    #[serde(rename = "u")]
    pub final_update_id: u64, // 从上次推送至今新增的最后一个 update Id
    #[serde(rename = "b")]
    pub bids: Vec<(String, String)>, // 买盘更新 [价格, 数量]
    #[serde(rename = "a")]
    pub asks: Vec<(String, String)>, // 卖盘更新 [价格, 数量]
}

/// 深度数据状态管理
#[derive(Debug, Clone)]
pub struct DepthState {
    pub snapshot: Option<DepthSnapshot>,
    pub last_update_id: u64,
    pub is_initialized: bool,
}

/// 24小时交易量统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Volume24hStats {
    pub symbol: String,
    pub spot_volume: f64,    // 现货24小时交易量
    pub futures_volume: f64, // 期货24小时交易量
    pub total_volume: f64,   // 总交易量
    pub spot_ratio: f64,     // 现货占比 (%)
    pub futures_ratio: f64,  // 期货占比 (%)
    pub last_updated: DateTime<Utc>,
}

impl Default for DepthState {
    fn default() -> Self {
        Self {
            snapshot: None,
            last_update_id: 0,
            is_initialized: false,
        }
    }
}

impl Default for Kline {
    fn default() -> Self {
        Self {
            symbol: String::new(),
            open_time: 0,
            close_time: 0,
            open_price: 0.0,
            high_price: 0.0,
            low_price: 0.0,
            close_price: 0.0,
            volume: 0.0,
            quote_volume: 0.0,
            trade_count: 0,
            taker_buy_volume: 0.0,
            taker_buy_quote_volume: 0.0,
            is_closed: false,
        }
    }
}

impl Default for Volume24hStats {
    fn default() -> Self {
        Self {
            symbol: String::new(),
            spot_volume: 0.0,
            futures_volume: 0.0,
            total_volume: 0.0,
            spot_ratio: 0.0,
            futures_ratio: 0.0,
            last_updated: Utc::now(),
        }
    }
}
