# Trading UI - 专业CLI交易面板

一个功能完整的CLI交易面板，支持实时数据展示和快速命令操作。采用模块化架构，使用Rust构建，具有高性能和低延迟特性。

## 🚀 功能特性

### 📊 数据展示
1. **币安现货**
   - 实时深度数据（买卖盘前5档）
   - 市场成交记录（最近5笔）
   - 未实现盈亏统计

2. **币安期货**
   - 实时深度数据（买卖盘前5档）
   - 个人成交记录（最近5笔）
   - 未实现盈亏统计

3. **多交易所持仓**
   - 建仓价格和标记价格
   - 实时未实现盈亏计算
   - 持仓方向（多头/空头）
   - 支持币安、OKX、Bybit等主流交易所

### ⚡ 操作功能
1. **现货/期货交易**
   - 市价单和限价单支持
   - 快速买入/卖出操作
   - 智能平仓功能（支持部分平仓和全部平仓）

2. **资产管理**
   - 多交易所提币功能
   - 地址验证（支持BTC、ETH等主流币种）
   - 实时余额查询

3. **风险管理**
   - 实时PnL监控
   - 持仓风险提醒
   - 交易历史记录

## 🛠️ 技术架构

### 核心模块
```
src/
├── types.rs          # 数据类型定义
├── state.rs          # 应用状态管理
├── commands.rs       # 命令解析器
├── ui/               # 用户界面模块
│   ├── app.rs        # 应用主逻辑
│   └── components.rs # UI组件
├── websocket/        # WebSocket通信模块
│   ├── server.rs     # WebSocket服务器
│   ├── client.rs     # WebSocket客户端
│   └── handler.rs    # 消息处理器
├── data/             # 数据处理模块
│   ├── mock.rs       # 模拟数据生成器
│   └── generator.rs  # 数据生成接口
└── utils/            # 工具模块
    ├── logging.rs    # 日志系统
    ├── config.rs     # 配置管理
    └── validation.rs # 数据验证
```

### 技术栈
- **UI框架**: ratatui (高性能终端UI)
- **异步运行时**: tokio (高并发处理)
- **WebSocket**: tokio-tungstenite (实时通信)
- **状态管理**: DashMap (线程安全的哈希表)
- **日志系统**: tracing (结构化日志)
- **配置管理**: toml (配置文件解析)

## 📦 安装和运行

### 前置要求
- Rust 1.70+
- Python 3.7+ (用于测试客户端，可选)

### 快速开始
```bash
# 克隆项目
git clone <repository-url>
cd trading-ui

# 编译并运行
cargo run

# 或者编译发布版本
cargo build --release
./target/release/trading-ui
```

### 测试应用程序
```bash
# 运行启动测试
python3 test_run.py

# 测试WebSocket连接
pip install websockets
python3 test_client.py
```

## 🎮 使用说明

### 界面导航
- **Tab键**: 切换标签页 (Binance / Positions / Operations)
- **q键**: 退出程序
- **:键**: 进入命令模式
- **Esc键**: 退出命令模式
- **h键**: 显示帮助信息

### 命令系统

#### 交易命令
```bash
# 限价买入
:buy binance BTCUSDT 0.1 45000

# 市价卖出
:sell okx ETHUSDT 1.0

# 部分平仓
:close binance BTCUSDT 0.5

# 全部平仓
:close binance BTCUSDT
```

#### 资产管理
```bash
# 提币到外部地址
:withdraw binance BTC 0.1 **********************************
:withdraw okx ETH 1.0 ******************************************
```

#### 帮助命令
```bash
# 显示帮助信息
:help
```

### 界面布局详解

#### 1. Binance标签页
显示币安现货和期货的实时数据：
- **深度数据**: 实时买卖盘信息
- **成交记录**: 市场成交和个人成交
- **PnL统计**: 实时盈亏计算

#### 2. Positions标签页
显示所有交易所的持仓信息：
- **多交易所支持**: 币安、OKX、Bybit等
- **详细持仓信息**: 持仓大小、入场价格、标记价格
- **实时PnL**: 动态计算未实现盈亏

#### 3. Operations标签页
显示操作帮助和命令说明：
- **命令格式**: 详细的命令使用说明
- **支持的交易所**: 当前支持的交易所列表
- **快捷键说明**: 界面操作指南

## 🔧 配置管理

### 配置文件 (config.toml)
```toml
[websocket]
bind_address = "127.0.0.1"
port = 8080
max_connections = 100
heartbeat_interval = 30

[ui]
refresh_rate = 60
default_tab = 0
max_trade_history = 50
theme = "default"

[data]
update_interval_ms = 500
symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
exchanges = ["binance", "okx", "bybit"]
enable_mock_data = true

[logging]
level = "info"
log_to_file = true
log_dir = "logs"
rotation = "daily"
```

## 🌐 WebSocket API

### 服务器信息
- **地址**: `ws://localhost:8080`
- **协议**: JSON消息格式
- **认证**: 当前版本无需认证（仅用于演示）

### 消息格式

#### 下单请求
```json
{
  "type": "PlaceOrder",
  "exchange": "binance",
  "symbol": "BTCUSDT",
  "side": "BUY",
  "quantity": 0.1,
  "price": 45000.0,
  "order_type": "LIMIT"
}
```

#### 平仓请求
```json
{
  "type": "ClosePosition",
  "exchange": "binance",
  "symbol": "BTCUSDT",
  "quantity": 0.1
}
```

#### 提币请求
```json
{
  "type": "Withdraw",
  "exchange": "binance",
  "asset": "BTC",
  "amount": 0.1,
  "address": "**********************************"
}
```

#### 响应格式
```json
{
  "type": "CommandResult",
  "success": true,
  "message": "Command executed successfully",
  "order_id": "uuid-string"
}
```

## 🔍 日志系统

### 日志级别
- **TRACE**: 详细的调试信息
- **DEBUG**: 调试信息
- **INFO**: 一般信息（默认）
- **WARN**: 警告信息
- **ERROR**: 错误信息

### 日志输出
- **控制台**: 彩色输出，实时显示
- **文件**: 按日轮转，保存在 `logs/` 目录
- **结构化**: 支持JSON格式，便于分析

### 专用日志宏
```rust
// 交易日志
log_trade!(info, "binance", "BTCUSDT", "BUY", 0.1, 45000.0);

// 订单日志
log_order!(info, "order-id", "binance", "BTCUSDT", "BUY", 0.1, 45000.0, "LIMIT");

// 持仓日志
log_position!(info, "binance", "BTCUSDT", 1.0, 44000.0, 1000.0);
```

## 🚧 开发指南

### 添加新交易所
1. 在 `types.rs` 中添加交易所枚举
2. 在 `data/mock.rs` 中添加模拟数据
3. 在 `commands.rs` 中添加命令支持
4. 更新UI组件显示逻辑

### 连接真实API
1. 替换 `data/mock.rs` 中的模拟数据生成器
2. 实现真实的WebSocket连接
3. 添加API密钥管理
4. 实现错误处理和重连机制

### 扩展命令系统
1. 在 `types.rs` 中添加新的消息类型
2. 在 `commands.rs` 中添加命令解析逻辑
3. 在 `websocket/handler.rs` 中添加处理逻辑
4. 更新帮助文档

### 自定义UI主题
1. 在 `ui/components.rs` 中修改颜色配置
2. 添加主题配置选项
3. 实现主题切换功能

## ⚠️ 注意事项

### 安全提醒
- **模拟环境**: 当前版本使用模拟数据，不会执行真实交易
- **API安全**: 生产环境需要添加API密钥管理和加密
- **网络安全**: WebSocket服务器需要添加认证和授权机制

### 性能优化
- **内存管理**: 定期清理历史数据，避免内存泄漏
- **网络优化**: 实现连接池和请求限流
- **UI优化**: 避免频繁重绘，优化渲染性能

### 风险管理
- **数据验证**: 严格验证所有输入数据
- **错误处理**: 完善的错误处理和恢复机制
- **监控告警**: 实现关键指标监控和异常告警

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至项目维护者
- 加入项目讨论群
