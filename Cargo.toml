[package]
name = "trading-ui"
version = "0.1.0"
edition = "2024"

[dependencies]
# Terminal UI
ratatui = { version = "0.23", features = ["crossterm"] }
crossterm = "0.27"
# Async & state
tokio = { version = "1.37", features = ["full"] }
dashmap = "5"
# JSON parsing
simd-json = "0.13"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
# WebSocket
tokio-tungstenite = { version = "0.20", features = ["rustls-tls-webpki-roots"] }
tungstenite = "0.20"
futures-util = "0.3"
url = "2.4"
tokio-socks = "0.5"
base64 = "0.21"
# Additional utilities
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "chrono"] }
tracing-appender = "0.2"
# Configuration
toml = "0.8"
reqwest = { version = "0.12.22", features = ["json", "rustls-tls"], default-features = false }

[dev-dependencies]
tempfile = "3.0"
